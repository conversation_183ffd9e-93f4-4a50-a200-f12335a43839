# PS-2023 WordPress Theme Development Guidelines

This document provides comprehensive project-specific knowledge for developers working on the PS-2023 WordPress theme.

## Build & Configuration Instructions

### Setup Process

1. **Clone the repository**
   ```bash
   git clone [repository-url]
   cd ProgresivneSlovensko-PS
   ```

2. **Environment Configuration**
   - Create a `.env.local` file in the project root with required environment variables
   - Create a `.env` file in the theme directory (`src/wp-content/themes/ps-2023/.env`)

3. **Install Dependencies**
   ```bash
   # Install PHP dependencies for the theme
   cd src/wp-content/themes/ps-2023
   composer install
   
   # Install JavaScript dependencies
   npm install
   
   # Return to project root
   cd ../../../..
   ```

4. **Start Local Development Environment**
   ```bash
   make start
   ```
   This command will:
   - Pull Docker images
   - Build containers
   - Start the development environment

5. **Install WordPress Must-Use Plugins**
   ```bash
   make install_mu_plugins
   ```

### Environment Variables

#### Root `.env.local` (for Docker)
- `LOCAL_PORT`: Port for local development (used by Nginx proxy)

#### Theme `.env` (src/wp-content/themes/ps-2023/.env)
- `APP_NAME`: Application name identifier (e.g., "peticny")
- `QOMON_API_URL`: URL for Qomon API (e.g., "https://incoming.qomon.app")
- `QOMON_API_KEY`: API key for Qomon authentication

### Dependencies

#### PHP Dependencies (via Composer)
- Roots/Acorn: Laravel-inspired framework for WordPress
- Other dependencies as specified in `composer.json`

#### JavaScript Dependencies (via npm)
- @roots/bud: Build tool for WordPress themes
- Tailwind CSS: Utility-first CSS framework
- Other dependencies as specified in `package.json`

### Build Commands

#### Main Build Commands
- `make all`: Build the entire project
- `make ps`: Build for production deployment
- `make ps-stage`: Build for staging deployment
- `make mep`: Build for MEP site deployment
- `make peticie`: Build for petitions site deployment
- `make start`: Start local development environment
- `make clean`: Clean build artifacts and logs

#### Theme-specific Build Commands
- `npm run build`: Build theme assets
- `npm run build --filter=mep`: Build theme assets for MEP variant
- `npm run build --filter=peticie`: Build theme assets for petitions variant

### Troubleshooting

#### Common Setup Issues

1. **Docker Networking Issues**
   - Problem: Containers can't communicate with each other
   - Solution: Check if the `ps-2023-network` network exists and is properly configured

2. **Database Connection Issues**
   - Problem: WordPress can't connect to MySQL
   - Solution: Verify MySQL container is running and credentials match in docker-compose.yaml

3. **Composer Dependency Issues**
   - Problem: "Error locating autoloader" message
   - Solution: Run `composer install` in the theme directory

4. **Node.js Build Issues**
   - Problem: Build fails with JavaScript errors
   - Solution: Check Node.js version compatibility and run `npm install` to ensure all dependencies are installed

## Development Guidelines

### Architecture Overview

The PS-2023 theme uses a modern WordPress development approach with:

1. **Roots/Acorn Framework**
   - Laravel-inspired architecture for WordPress
   - Service container for dependency injection
   - Blade templating engine

2. **Directory Structure**
   - `app/`: PHP classes organized by functionality
     - `API/`: API endpoints
     - `Config/`: Configuration files
     - `Controllers/`: Business logic
     - `Fields/`: Custom fields (likely ACF)
     - `Gutenberg/`: Custom Gutenberg blocks
     - `Hooks/`: WordPress hooks
     - `Model/`: Data models
     - `Posts/`: Custom post types
     - `Providers/`: Service providers
     - `Settings/`: Theme settings
     - `View/`: View-related functionality
   - `resources/`: Frontend assets and templates
     - `views/`: Blade templates
     - `styles/`: CSS/SCSS files
     - `scripts/`: JavaScript files
   - `public/`: Compiled assets

3. **Auto-Registration System**
   - Components are automatically registered via auto-registration files
   - Configuration can exclude specific components from auto-registration

### Code Style Conventions

1. **PHP Coding Standards**
   - PSR-4 autoloading
   - Class names in PascalCase
   - Method names in camelCase
   - Use type hints and return types
   - Document methods with PHPDoc comments

2. **JavaScript/TypeScript Conventions**
   - Use ES6+ syntax
   - Follow TypeScript typing conventions
   - Component-based architecture

3. **CSS/SCSS Conventions**
   - Tailwind CSS utility-first approach
   - Custom components for reusable UI elements
   - Variant-specific styles in separate files

### Design Patterns

1. **MVC-like Pattern**
   - Controllers handle business logic
   - Models represent data structures
   - Views (Blade templates) handle presentation

2. **Service Provider Pattern**
   - Register services in the container
   - Use dependency injection for components

3. **Repository Pattern**
   - Abstract data access through repositories
   - Separate business logic from data retrieval

### Performance Considerations

1. **API Optimization**
   - Use pagination for large datasets (e.g., QomonSignature controller)
   - Cache API responses when appropriate
   - Implement error handling and retries

2. **Asset Optimization**
   - Use Bud for asset bundling and optimization
   - Lazy load images and non-critical resources
   - Implement proper caching strategies

3. **Database Optimization**
   - Use transients for caching
   - Optimize WordPress queries
   - Implement proper indexing for custom tables

### Known Limitations and Edge Cases

1. **Multi-site Deployment**
   - The theme supports multiple site variants (main, mep, peticie)
   - Each variant has its own Tailwind configuration
   - Build process requires specific filters for each variant

2. **API Dependencies**
   - Qomon API integration for signatures
   - Handle API rate limits and downtime gracefully

3. **WordPress Version Compatibility**
   - Ensure compatibility with the WordPress version used in production
   - Test with the latest WordPress version for future compatibility

## Project-Specific Workflows

### Code Review Process

1. **Pre-submission Checklist**
   - Run code style checks
   - Test on local environment
   - Verify compatibility with all site variants

2. **Review Criteria**
   - Code follows project conventions
   - No performance regressions
   - Proper error handling
   - Documentation for new features

### Deployment Process

1. **Staging Deployment**
   ```bash
   make ps-stage
   ```
   - Builds theme assets
   - Deploys to staging server via rsync
   - Excludes vendor directories and .env files

2. **Production Deployment**
   ```bash
   make ps
   ```
   - Builds theme assets
   - Deploys to production server via rsync
   - Excludes vendor directories and .env files

3. **Variant Deployments**
   ```bash
   # For MEP site
   make mep
   
   # For petitions site
   make peticie
   ```
   - Builds variant-specific assets
   - Deploys to respective servers

### Feature Flagging

1. **Environment-based Configuration**
   - Use .env files for environment-specific settings
   - Define constants for feature flags

2. **Variant-specific Features**
   - Use build filters to enable/disable features per variant
   - Implement conditional logic based on APP_NAME

3. **Testing New Features**
   - Develop features in isolation
   - Test thoroughly on staging before production deployment
   - Use feature branches for development

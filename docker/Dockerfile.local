# Image and working directory.
FROM node:20
WORKDIR /app

# Install dependencies.
COPY ./src/wp-content/themes/ps-2023/package*.json ./
RUN npm ci

# Install composer and php dependencies.
RUN apt-get update && apt-get install -y php php-xml composer && apt-get clean && rm -rf /var/lib/apt/lists/*

# Copy PHP Composer files.
COPY ./src/wp-content/themes/ps-2023/composer.* ./

# Copy source code.
COPY ./src/wp-content/themes/ps-2023 ./

# Install composer dependencies.
RUN composer install

# Run the development server, first go into the src directory.
CMD ["npm", "run", "build"]

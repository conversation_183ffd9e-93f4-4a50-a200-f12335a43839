name: ps-2023

services:
  ps-2023-db:
    image: mysql
    restart: no
    environment:
      MYSQL_DATABASE: wp
      MYSQL_USER: wp
      MYSQL_PASSWORD: pass
      MYSQL_RANDOM_ROOT_PASSWORD: '1'
    volumes:
      - ./data/db:/var/lib/mysql
    networks:
      - ps-2023-network

  ps-2023-wp:
    build:
      context: ../
      dockerfile: ./docker/DockerfileWP.local
    restart: no
    environment:
      WORDPRESS_DB_HOST: ps-2023-db
      WORDPRESS_DB_NAME: wp
      WORDPRESS_DB_USER: wp
      WORDPRESS_DB_PASSWORD: pass
      WORDPRESS_DEBUG: 1
      WORDPRESS_CONFIG_EXTRA:
        define('WP_DEBUG', true);
        define('WP_DEBUG_LOG', true);
        define('WP_DEBUG_DISPLAY', false);
      XDEBUG_MODE: debug
      XDEBUG_CONFIG: remote_enable=1 remote_host=host.docker.internal remote_port=9003 remote_autostart=1
    volumes:
      - ./data/wp:/var/www/html
      - ../src/wp-content/themes/ps-2023:/var/www/html/wp-content/themes/ps-2023
      - ../src/wp-content/plugins:/var/www/html/wp-content/plugins
      - ../uploads.ini:/usr/local/etc/php/conf.d/uploads.ini
    depends_on:
      - ps-2023-db
    networks:
      - ps-2023-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

  wp-cli:
    image: wordpress:cli
    volumes:
      - ./data/wp:/var/www/html
      - ../src/wp-content/themes/ps-2023:/var/www/html/wp-content/themes/ps-2023
    depends_on:
      - ps-2023-db
    networks:
      - ps-2023-network
    environment:
      WORDPRESS_DB_HOST: ps-2023-db
      WORDPRESS_DB_NAME: wp
      WORDPRESS_DB_USER: wp
      WORDPRESS_DB_PASSWORD: pass

  ps-2023-plugin:
    build:
      context: ../
      dockerfile: ./docker/Dockerfile.local
    volumes:
      - ../src:/app
    ports:
      - '3000:3000'
    depends_on:
      - ps-2023-wp
    networks:
      - ps-2023-network

  adminer:
    image: adminer
    restart: no
    networks:
      - ps-2023-network

  proxy:
    image: nginx
    restart: no
    volumes:
      - ./proxy/default.conf:/etc/nginx/conf.d/default.conf
      - ./proxy/nginx.conf:/etc/nginx/nginx.conf
    ports:
      - '${LOCAL_PORT}:80'
    depends_on:
      - ps-2023-wp
      - ps-2023-plugin
    networks:
      - ps-2023-network

networks:
  ps-2023-network:
    name: ps-2023-network  # Explicitný názov siete
    driver: bridge

#!/bin/bash

# Root directory
root_dir="$(dirname "$(realpath "$0")")/.."
cd "$root_dir" || exit 1

# Go to WordPress root directory
cd "docker/data/wp/wp-content/plugins/" || exit 1

# List of plugin urls to download zip files
# example: "https://downloads.wordpress.org/plugin/woocommerce.9.1.4.zip"
# shellcheck disable=SC2054
plugin_urls=()

# Download and extract plugins
for plugin_url in "${plugin_urls[@]}"; do
  plugin_zip_file="${plugin_url##*/}"
  plugin_dir="${plugin_zip_file%.zip}"
  if [ ! -f "${plugin_zip_file%.zip}.zip" ]; then
    echo "Downloading $plugin_zip_file..."
    curl -s -o "$plugin_zip_file" "$plugin_url"
    unzip -q "$plugin_zip_file"
  else
    echo "$plugin_dir already exists"
  fi
done

# Check if folder "plugins" exists in root directory
if [ -d "$root_dir/plugins" ]; then
  # Copy plugins from root directory to WordPress plugins directory
  rsync -a "$root_dir/plugins/" "./"

  echo "Plugins have been installed"
fi

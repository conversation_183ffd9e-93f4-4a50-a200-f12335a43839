mkdir -p './src/resources/views/components/icons'

# First go to folder with SVG icons
cd './src/resources/images/icons' || exit;

# Loop through all SVG files
for file in *.svg; do
  # Get the filename without the extension
  filename=$(basename -- "$file")
  filename="${filename%.*}"

  # Načítanie obsahu súboru .svg
  svgContent=$(<"$file")

  if [ -z "$svgContent" ]; then
    echo "Nepodarilo sa načítať obsah súboru SVG: $file"
    continue
  fi

  # Pridanie poznámky na začiatok súboru, že je tento súbor generovaný a nemá sa editovať!
  svgContent="@php // Tento súbor bol vygenerovaný pomocou skriptu bin/icon-generator.sh a needitovať! @endphp"$'\n'"$svgContent"

  # Pridanie triedy do SVG tagu
  svgContentWithClass=$(echo "$svgContent" | sed -e "s/<svg/<svg class=\"icon icon-$filename\"/")

  # Zápis obsahu do nového .blade.php súboru
  bladeFilePath="../../views/components/icons/icon-$filename.blade.php"
  echo "$svgContentWithClass" > "$bladeFilePath"

  echo "Vytvorený súbor: $bladeFilePath"


  # Pridanie súboru .scss do, ktorého sa vytvoria importy pre všetky ikony ako mixin pre použitie v SCSS súboroch
  scssFilePath="../../styles/_icons.scss"

  # Ak súbor neexistuje, vytvoríme ho a pridáme mixin pre ikonu
  if [ ! -f "$scssFilePath" ]; then
        echo "@mixin icon-$filename(\$size: 'w-6', \$color: 'bg-blue-imf-700') {
                &:before {
                  content: '';
                  @apply absolute left-0 top-0 h-full #{\$size} #{\$color};
                  mask-image: url('/images/icons/$filename.svg');
                  mask-size: contain;
                  mask-repeat: no-repeat;
                  mask-position: center;
                }
              }" > "$scssFilePath"
  else
    # Ak súbor existuje a nie je prázdny, skontrolujeme, či mixin pre danú ikonu už neexistuje
    if [ -s "$scssFilePath" ]; then
      if grep -q "@mixin icon-$filename" "$scssFilePath"; then
        echo "Mixin pre ikonu: $filename už existuje v súbore: $scssFilePath"
        continue
      fi
    fi

    # Ak mixin pre danú ikonu neexistuje, pridáme ho na koniec súboru
    echo "@mixin icon-$filename(\$size: 'w-6', \$color: 'bg-blue-imf-700') {
            &:before {
              content: '';
              @apply absolute left-0 top-0 h-full #{\$size} #{\$color};
              mask-image: url('/images/icons/$filename.svg');
              mask-size: contain;
              mask-repeat: no-repeat;
              mask-position: center;
            }
          }" >> "$scssFilePath"
  fi

  echo "Pridaný mixin pre ikonu: $filename do súboru: $scssFilePath"

done

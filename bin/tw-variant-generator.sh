#!/bin/bash

if [ -z "$1" ]; then
  echo "Chýba názov varianty! Použitie: ./tw-variant-generator.sh <názov_varianty>"
  exit 1
fi

VARIANT=$1
BASE_DIR="$(dirname "$0")"

# 1. Vytvorenie Tailwind konfigurácie pre variant
cat > "../src/wp-content/themes/ps-2023/tailwind.${VARIANT}.config.cjs" << EOF
const baseConfig = require('./tailwind.config.cjs');

module.exports = {
  ...baseConfig,
  content: baseConfig.content,
  theme: {
    ...baseConfig.theme,
    extend: {
      ...baseConfig.theme.extend,
      colors: {
        ...baseConfig.theme.extend.colors,
        // Pridajte vlastné farby pre variant ${VARIANT} tu
      },
    },
  },
};
EOF

# 2. Aktualizácia bud.config.ts
INSERTION_POINT="// -- Add new variants below --"
NEW_CODE="  if (activeFilters.includes('${VARIANT}')) {\n    await app.make('${VARIANT}', async (${VARIANT}) => {\n      await configureVariant(${VARIANT}, './tailwind.${VARIANT}.config.cjs');\n    });\n  }\n\n  ${INSERTION_POINT}"

if ! grep -q "await app.make('${VARIANT}'" "../src/wp-content/themes/ps-2023/bud.config.ts"; then
  sed -i.bak "s~${INSERTION_POINT}~${NEW_CODE}~" "../src/wp-content/themes/ps-2023/bud.config.ts"
  rm "../src/wp-content/themes/ps-2023/bud.config.ts.bak"
fi

# 3. Vytvorenie CSS súboru pre variant 
mkdir -p "../src/wp-content/themes/ps-2023/resources/styles/variants"
cat > "../src/wp-content/themes/ps-2023/resources/styles/variants/${VARIANT}.scss" << EOF
@config "../../../tailwind.${VARIANT}.config.cjs";

@import "../app";
EOF

# 4. Pridanie do Makefile nového variantu neičo na tento štýl
if ! grep -q "${VARIANT}:" ../Makefile; then
  echo -e "\n${VARIANT}:\n\t./bin/build.sh ${VARIANT}" >> ../Makefile
fi

# 5. Pridanie deploy conditon do build.sh
INSERTION_POINT="# HERE generate new deploy condition" 
NEW_CODE="if [ \"\$1\" == \"${VARIANT}\" ]; then\n    echo \"\${bold} Deploying ${VARIANT} assets to the prod server \${normal}\"\n    //rsync -avz --delete --exclude=vendor -e 'ssh -p 22' \"\$out\" \"{user}@{server}:{path}\" || exit \"\$?\"\nfi\n\n${INSERTION_POINT}"

if ! grep -q "if \[ \"\$1\" == \"${VARIANT}\"" "../bin/build.sh"; then
  sed -i.bak "s~${INSERTION_POINT}~${NEW_CODE}~" "../bin/build.sh"
  rm "../bin/build.sh.bak"
fi

echo "✅ Variant ${VARIANT} úspešne vytvorený!"
echo "➡️ Upravte farby v tailwind.${VARIANT}.config.ts"
echo "➡️ Spustite kompiláciu: yarn bud build --filter ${VARIANT}"

#!/bin/bash

# Root directory
cd "$( dirname "$( realpath "$0" )" )/.." || exit 1

# Variables
bold=$(tput bold)
normal=$(tput sgr0)

src="./src/wp-content/themes/ps-2023"
out="./build/"

# Clear everything in out directory except .gitignore
rm -rf "$out"* || exit "$?"

# Languages
# wp i18n make-pot "./$plugin/" "./$plugin/languages/$plugin.pot" || exit "$?"

# Define other procedures before the build
# ...

cd "$src" && npm run build --filter="$1" && cd '../../../../' || exit "$?"

rsync -av --exclude={.[!.]*,node_modules,vendor,bud.config.js,package*,yarn.lock} "$src/" "$out" || exit "$?"

echo "${bold} The build has been successful ${normal}"



# If is called with "ps-stage" argument, deploy to the production server
if [ "$1" == "ps-stage" ]; then
    echo "${bold} Deploying to the stage server ${normal}"
    rsync -avz --delete --exclude=vendor --exclude=.env -e 'ssh -p 22' "$out" "root@195.210.28.156:/var/www/stage.progresivne.sk/wp-content/themes/ps-2023/" || exit "$?"
fi

# If is called with "ps" argument, deploy to the production server
if [ "$1" == "ps" ]; then
    echo "${bold} Deploying to the prod server ${normal}"
    rsync -avz --delete --exclude=vendor --exclude=.env -e 'ssh -p 22' "$out" "root@195.210.28.156:/var/www/progresivne.sk/wp-content/themes/ps-2023/" || exit "$?"
fi

if [ "$1" == "peticie" ]; then
    echo "${bold} Deploying peticie assets to the prod server ${normal}"
    rsync -avz --exclude=vendor --exclude=.env -e 'ssh -p 22' "$out" "root@195.210.29.227:/var/www/peticie.progresivne.sk/wp-content/themes/ps-2023/" || exit "$?"
fi

if [ "$1" == "mep" ]; then
    echo "${bold} Deploying mep assets to the prod server ${normal}"
    rsync -avz --exclude=vendor --exclude=.env -e 'ssh -p 22' "$out" "root@195.210.29.169:/var/www/mep.progresivne.sk/wp-content/themes/ps-2023/" || exit "$?"
fi

# HERE generate new deploy condition

# Deploy to the staging if no argument is provided
if [ "$1" == "" ]; then
    echo "${bold} Deploying to the staging server ${normal}"
    #rsync -avz --delete --exclude=vendor -e 'ssh -p 29567' "$out" "[SSH]/sub/kvetinaren/wp-content/themes/[name]-theme/" || exit "$?"
fi

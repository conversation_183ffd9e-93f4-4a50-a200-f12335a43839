<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Animation Timing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #000080;
            color: white;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .word-container {
            position: relative;
            width: 100%;
            height: 100px;
            border: 2px solid #fff;
            margin: 20px 0;
        }
        
        .word {
            position: absolute;
            top: 0%;
            max-width: 1200px;
        }
        
        .word span {
            font-family: "Arial", sans-serif;
            color: #fff;
            display: inline-block;
            font-variation-settings: "wdth" var(--wd);
            line-height: 95%;
            font-size: 3rem;
            font-weight: bold;
        }
        
        .word-container .word span {
            animation: stretch 3s cubic-bezier(.75, 1.45, .14, 1) infinite alternate;
        }
        
        @keyframes stretch {
            0% {
                font-variation-settings: "wdth" 60;
                transform: scaleX(0.6);
            }
            33% {
                font-variation-settings: "wdth" calc(var(--wd) * 0.99);
                transform: scaleX(0.99);
            }
            100% {
                font-variation-settings: "wdth" var(--wd);
                transform: scaleX(1);
            }
        }
        
        .log {
            background: #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Animation Timing Test</h1>
        <p>This test verifies that the setDynamicWordPosition function waits for the animation to reach maximum width.</p>
        
        <div class="word-container">
            <div class="word" id="animated-word">
                <span style="--wd:61">T</span>
                <span style="--wd:69">R</span>
                <span style="--wd:100">E</span>
                <span style="--wd:121">S</span>
                <span style="--wd:150">K</span>
                <span style="--wd:92">!</span>
            </div>
        </div>
        
        <div class="log" id="log"></div>
        
        <button onclick="testPositioning()">Test Positioning</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function testPositioning() {
            log('Starting positioning test...');
            
            const wordElement = document.getElementById('animated-word');
            if (!wordElement) {
                log('ERROR: animated-word element not found');
                return;
            }

            const wordContainer = wordElement.closest('.word-container');
            if (!wordContainer) {
                log('ERROR: word-container not found');
                return;
            }

            // Log initial measurements
            log(`Initial word width: ${wordElement.offsetWidth}px`);
            log(`Container width: ${wordContainer.offsetWidth}px`);
            
            // Test the timing - measure width at different intervals
            const intervals = [0, 1000, 2000, 3000, 3100, 4000, 5000, 6000];
            
            intervals.forEach(delay => {
                setTimeout(() => {
                    const wordWidth = wordElement.offsetWidth;
                    const containerWidth = wordContainer.offsetWidth;
                    log(`At ${delay}ms: word width = ${wordWidth}px, container width = ${containerWidth}px`);
                    
                    if (delay === 3100) {
                        log('>>> This is when setDynamicWordPosition calculates positioning <<<');
                        
                        const availableSpace = containerWidth - wordWidth;
                        const spacePercentage = wordWidth > 0 ? (availableSpace / wordWidth) : 0;
                        const leftPosition = Math.max(0, availableSpace / 2);
                        
                        log(`Available space: ${availableSpace}px`);
                        log(`Space percentage: ${(spacePercentage * 100).toFixed(2)}%`);
                        log(`Calculated left position: ${leftPosition}px`);
                        
                        if (spacePercentage <= 0.05) {
                            log('Container too tight - no positioning applied');
                        } else {
                            log('Positioning applied');
                            wordElement.style.left = `${leftPosition}px`;
                        }
                    }
                }, delay);
            });
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // Auto-start test when page loads
        window.addEventListener('load', () => {
            log('Page loaded - animation started');
            setTimeout(() => {
                testPositioning();
            }, 500);
        });
    </script>
</body>
</html>

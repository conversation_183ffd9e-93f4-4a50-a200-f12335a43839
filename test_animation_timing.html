<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Animation Timing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #000080;
            color: white;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .word-container {
            position: relative;
            width: 100%;
            height: 100px;
            border: 2px solid #fff;
            margin: 20px 0;
        }
        
        .word {
            position: absolute;
            top: 0%;
            max-width: 1200px;
        }
        
        .word span {
            font-family: "Arial", sans-serif;
            color: #fff;
            display: inline-block;
            font-variation-settings: "wdth" var(--wd);
            line-height: 95%;
            font-size: 3rem;
            font-weight: bold;
        }
        
        .word-container:hover .word span {
            animation: stretch-cycle 6s cubic-bezier(.75, 1.45, .14, 1) infinite;
        }
        
        /* K letter special animation to preserve 15% extra scaling */
        .word-container:hover .word span:nth-child(5) {
            animation: stretch-cycle-k 6s cubic-bezier(.75, 1.45, .14, 1) infinite;
        }
        
        @keyframes stretch-cycle {
            /* 0-1s: Expand phase (0% to 16.67% of 6s cycle) */
            0% {
                font-variation-settings: "wdth" 60;
                transform: scaleX(0.6);
            }
            16.67% {
                font-variation-settings: "wdth" var(--wd);
                transform: scaleX(1);
            }
            
            /* 1-3s: Hold expanded phase (16.67% to 50% of 6s cycle) */
            50% {
                font-variation-settings: "wdth" var(--wd);
                transform: scaleX(1);
            }
            
            /* 3-4s: Contract phase (50% to 66.67% of 6s cycle) */
            66.67% {
                font-variation-settings: "wdth" 60;
                transform: scaleX(0.6);
            }
            
            /* 4-6s: Hold condensed phase (66.67% to 100% of 6s cycle) */
            100% {
                font-variation-settings: "wdth" 60;
                transform: scaleX(0.6);
            }
        }
        
        @keyframes stretch-cycle-k {
            /* 0-1s: Expand phase (0% to 16.67% of 6s cycle) */
            0% {
                font-variation-settings: "wdth" 60;
                transform: scaleX(0.69); /* 0.6 * 1.15 */
            }
            16.67% {
                font-variation-settings: "wdth" var(--wd);
                transform: scaleX(1.15); /* preserve 15% extra scaling */
            }
            
            /* 1-3s: Hold expanded phase (16.67% to 50% of 6s cycle) */
            50% {
                font-variation-settings: "wdth" var(--wd);
                transform: scaleX(1.15);
            }
            
            /* 3-4s: Contract phase (50% to 66.67% of 6s cycle) */
            66.67% {
                font-variation-settings: "wdth" 60;
                transform: scaleX(0.69); /* 0.6 * 1.15 */
            }
            
            /* 4-6s: Hold condensed phase (66.67% to 100% of 6s cycle) */
            100% {
                font-variation-settings: "wdth" 60;
                transform: scaleX(0.69); /* 0.6 * 1.15 */
            }
        }
        
        .log {
            background: #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Animation Timing Test - 6-Second Cycle</h1>
        <p>This test verifies the new 6-second cycle animation: 1s expand → 2s hold → 1s contract → 2s hold.</p>
        <p><strong>Hover over the word container to trigger the animation!</strong></p>
        
        <div class="word-container" id="word-container">
            <div class="word" id="animated-word">
                <span style="--wd:61">T</span>
                <span style="--wd:69">R</span>
                <span style="--wd:100">E</span>
                <span style="--wd:121">S</span>
                <span style="--wd:150">K</span>
                <span style="--wd:92">!</span>
            </div>
        </div>
        
        <div class="log" id="log"></div>
        
        <button onclick="testPositioning()">Test Positioning</button>
        <button onclick="simulateHover()">Simulate Hover</button>
        <button onclick="stopHover()">Stop Hover</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function simulateHover() {
            const wordContainer = document.getElementById('word-container');
            wordContainer.style.pointerEvents = 'none';
            wordContainer.classList.add('simulated-hover');
            
            // Add CSS for simulated hover
            if (!document.getElementById('hover-style')) {
                const style = document.createElement('style');
                style.id = 'hover-style';
                style.textContent = '.simulated-hover .word span { animation: stretch-cycle 6s cubic-bezier(.75, 1.45, .14, 1) infinite !important; } .simulated-hover .word span:nth-child(5) { animation: stretch-cycle-k 6s cubic-bezier(.75, 1.45, .14, 1) infinite !important; }';
                document.head.appendChild(style);
            }
            
            log('Hover simulation started - 6-second cycle animation triggered');
        }
        
        function stopHover() {
            const wordContainer = document.getElementById('word-container');
            wordContainer.style.pointerEvents = 'auto';
            wordContainer.classList.remove('simulated-hover');
            log('Hover simulation stopped');
        }

        function testPositioning() {
            log('Starting 6-second cycle positioning test...');
            log('Make sure to hover over the container or click "Simulate Hover" first!');
            
            const wordElement = document.getElementById('animated-word');
            if (!wordElement) {
                log('ERROR: animated-word element not found');
                return;
            }

            const wordContainer = wordElement.closest('.word-container');
            if (!wordContainer) {
                log('ERROR: word-container not found');
                return;
            }

            // Log initial measurements
            log(`Initial word width: ${wordElement.offsetWidth}px`);
            log(`Container width: ${wordContainer.offsetWidth}px`);
            
            // Test the timing - measure width at key points in 6-second cycle
            // 0s: start condensed, 1s: fully expanded, 3s: still expanded, 4s: condensed again, 6s: still condensed
            const intervals = [
                { time: 0, phase: 'Start (condensed)' },
                { time: 500, phase: 'Expanding (0.5s)' },
                { time: 1000, phase: 'Fully expanded (1s)' },
                { time: 1100, phase: 'Positioning calculation point (1.1s)' },
                { time: 2000, phase: 'Hold expanded (2s)' },
                { time: 3000, phase: 'End of hold expanded (3s)' },
                { time: 3500, phase: 'Contracting (3.5s)' },
                { time: 4000, phase: 'Fully contracted (4s)' },
                { time: 5000, phase: 'Hold contracted (5s)' },
                { time: 6000, phase: 'End of cycle (6s)' }
            ];
            
            intervals.forEach(({ time, phase }) => {
                setTimeout(() => {
                    const wordWidth = wordElement.offsetWidth;
                    const containerWidth = wordContainer.offsetWidth;
                    log(`${phase} - ${time}ms: word width = ${wordWidth}px`);
                    
                    if (time === 1100) {
                        log('>>> Optimal positioning calculation point (after 1s expansion) <<<');
                        
                        const availableSpace = containerWidth - wordWidth;
                        const spacePercentage = wordWidth > 0 ? (availableSpace / wordWidth) : 0;
                        const leftPosition = Math.max(0, availableSpace / 2);
                        
                        log(`Available space: ${availableSpace}px`);
                        log(`Space percentage: ${(spacePercentage * 100).toFixed(2)}%`);
                        log(`Calculated left position: ${leftPosition}px`);
                        
                        if (spacePercentage <= 0.05) {
                            log('Container too tight - no positioning applied');
                        } else {
                            log('Positioning applied');
                            wordElement.style.left = `${leftPosition}px`;
                        }
                    }
                }, time);
            });
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // Initialize when page loads
        window.addEventListener('load', () => {
            log('Page loaded - hover over the container or click "Simulate Hover" to start animation');
            log('Then click "Test Positioning" to measure the animation cycle');
        });
    </script>
</body>
</html>

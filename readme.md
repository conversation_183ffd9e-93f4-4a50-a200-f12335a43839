# Sage Theme in Docker

This is a Docker image for Sage theme development.

## Prerequisites

1. Docker
2. Docker Compose
3. Make
4. Composer
5. Node.js (>=16.0.0)
6. NPM or Yarn

## Installation

1. Clone this repository
2. Create `.env.local` file in the root directory, if not created
    1. Add the following line to the file: `LOCAL_PORT=8005`
    2. Add optional line to the file: `WP_DEBUG=true`
3. Go into `src/wp-content/themes/ps-2023` directory
4. Run `composer install`
5. Run `npm ci` or `yarn install`
6. Run `npm run build` or `yarn build` for the first time (it will create the `public` directory)

## Development
1. Run `make start` to start the container
2. Access the site at `http://localhost:8005`
3. For hot reloading, run `npm run dev` or `yarn dev` in the `src/wp-content/themes/ps-2023` directory.  
   You can use the `--filter` option to specify a filter, e.g., `npm run dev --filter=app`.

## Build
1. Run `make all` to build the theme
2. The theme will be available in the `build` directory

## Deployment

The `build.sh` script and `Makefile` support multiple deployment options:

- **Staging**: Run `make ps-stage` or `./bin/build.sh ps-stage` to deploy to the staging server.
- **Production**: Run `make ps` or `./bin/build.sh ps` to deploy to the production server.
- **Petitions**: Run `make peticie` or `./bin/build.sh peticie` to deploy petition assets to the production server.
- **MEP**: Run `make mep` or `./bin/build.sh mep` to deploy MEP assets to the production server.

If no argument is provided, the script defaults to deploying to the staging server.

## Additional Configuration

To properly configure the theme, you need to add a `.env` file into the theme directory. This file should include one of the following parameters based on your use case:

- `progresivne`
- `mep`
- `petition`

For example, your `.env` file might look like this:

```
APP_NAME=progresivne
```

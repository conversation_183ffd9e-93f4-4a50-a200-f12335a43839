.PHONY: start install cs-fix cs-check version clean

DOCKER_LOCAL := docker compose \
		--file docker/docker-compose.yaml \
		--env-file .env.local

all:
	./bin/build.sh

ps:
	./bin/build.sh ps

ps-stage:
	./bin/build.sh ps-stage

start:
	$(DOCKER_LOCAL) pull
	$(DOCKER_LOCAL) up \
			--build \
			--renew-anon-volumes \
			--remove-orphans \
			--force-recreate

clean:
	rm -rf './build/*'
	find logs -name '*.log' -type f -delete

install_mu_plugins:
	./bin/install_mu_plugins.sh

mep:
	./bin/build.sh mep

peticie:
	./bin/build.sh peticie
